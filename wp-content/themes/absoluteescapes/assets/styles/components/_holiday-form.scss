.holiday-blocks-wrapper {
    position: relative;
    margin-top: -115px;

    @media only screen and (max-width: 1360px) {
        margin-top: -60px;
    }


    &__inner {
        overflow: hidden;

        &--form {
            overflow: visible;
        }
    }

    &__container {


        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            width: 100%;
            padding: 0;
            max-width: none;
        }

        .row {
            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                margin: 0;

            }
        }

        .holiday-blocks-wrapper__col {
            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                padding: 0;

            }
        }

        &--form {
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            margin: 0 auto;
            height: 100%;



            .holiday-blocks-wrapper__row {
                height: 100%;
            }

            .holiday-blocks-wrapper__col {
                height: 100%;

                @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                    height: auto;
                }


            }
        }
    }

    &__col {
        &--enquiry-form {
            padding-top: 150px;

            // Remove padding-top from 576px to 991px
            @media only screen and (min-width: 576px) and (max-width: 991px) {
                padding-top: 0;
            }
        }
    }
}

.enquiry-form {
    position: sticky;
    top: 115px;
    z-index: 30;
    padding-bottom: 45px;
    transition: 300ms;
    max-width: 380px;

    a {
        text-decoration: none;
    }


    @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        height: 100%;
        z-index: 1000;
        padding: 60px 0;
        background: rgba($black, 0.5);
        overflow-x: hidden;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;

        &.active {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }


    }

    label {
        color: $bluegrey;
    }

    &__inner {
        position: relative;
        padding: 25px 30px;
        border: 1px solid $bluegrey;
        background: $white;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            padding: 25px 20px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            position: relative;
            top: 0;
            width: 100%;
            max-width: 375px;
            padding-top: 50px;
            margin: 0 auto;
        }
    }

    &__close {
        display: none;
        position: absolute;
        top: 15px;
        right: 20px;
        font-size: 2.2rem;
        color: $bluegrey;
        cursor: pointer;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: block;
        }
    }

    &__heading {
        margin-bottom: 0;
    }

    &__subheading {
        margin-bottom: 10px;
        font-family: $bodyfontfamily;
    }

    &__field {
        padding: 7px 0;
    }

    .select-wrapper {
        display: block;
        width: 100%;

        &:after {
            top: -3px;
            right: 20px;
            border-color: #282425;
        }
    }

    select {
        width: 100%;
        border-radius: 5px;
        height: 50px;
        padding: 0 45px 0 15px;
        font-size: 1.8rem;
        font-family: $bodyfontfamily;
        font-weight: 400;
    }

    input {
        width: 100%;
        border-radius: 5px;
        height: 50px;
        padding: 0 45px 0 15px;
        border-color: $bluegrey;
        font-size: 1.8rem;
        font-family: $bodyfontfamily;
        font-weight: 400;
        color: $bluegrey;
    }

    &__button-wrapper {
        padding: 8px 0;

        span {
            display: block;
            margin-bottom: 5px;
            color: $bluegrey;
        }
    }

    &__button {
        width: 100%;
    }

    &__text {
        padding: 15px 0;
        p, li {
            line-height: 1.4;

            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                margin: 0;
            }
        }
    }

    &__review {
        text-align: center;
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: none;
        }
    }

}


.enquiry-cta {
    position: fixed;
    z-index: 899;
    width: 100%;
    left: 0;
    bottom: 0;
    background: $white;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;


    @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
        opacity: 1;
        visibility: visible;
        pointer-events: auto;

        // Scroll hide/show behavior - similar to masthead
        &.is-visible {
            transition: 300ms;
            transform: translateY(0);
        }

        &.is-hidden {
            transition: 300ms;
            transform: translateY(100%);
        }
    }

    &__inner {
        padding: 0 20px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 15px;
        }
    }

    &__content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        padding: 15px 0;
        margin: 0 -10px;
    }

    &__review {
        padding: 0 10px;

        // Hide AITO logo on mobile
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            display: none;
        }
    }

    &__button-wrapper {
        padding: 0 10px;

        // Center button on mobile when AITO logo is hidden
        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            flex: 1;
            display: flex;
            justify-content: center;
        }

        .button {
            padding: 10px 20px;
        }
    }


}
