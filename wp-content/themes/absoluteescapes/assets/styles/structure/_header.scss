.masthead {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 299;
    width: 100%;
    background: $white;

    @media only screen and (min-width: 1161px) {
        padding: 0 25px;

        &.is-fixed {
            position: fixed;
            transform: translateY(calc(-100% - 50px));
        }

        &.is-visible {
            transition: 300ms;
            transform: translateY(0);
        }

        &.is-hidden {
            transition: 300ms;
            transform: translateY(calc(-100% - 50px));
        }
    }

    @media only screen and (max-width: 1160px) {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background: $bluegrey;
    }

    &--form-page {
        .masthead__inner {
            height: 135px;

            @media only screen and (max-width: 1160px) {
                height: 80px;
                padding: 0 15px;
            }
        }
    }

    &__inner {
        display: flex;
        flex-wrap: wrap;
        //justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 51;
        max-width: 1920px;
        padding: 0;
        margin: 0 auto;

        @media only screen and (max-width: 1160px) {
            height: 80px;
            padding: 0 15px;
        }
    }

    ul {
        list-style: none;
    }

    a {
        font-weight: 300;
        color: $bluegrey!important;
        text-decoration: none;

        &:hover,
        &:focus {
            color: $teal!important;
            text-decoration: none;
        }

        .menu-title {
            display: block;
            transition: none;
        }
    }

    &__logo {
        display: inline-block;
        vertical-align: middle;
        padding-right: 25px;
        max-width: 262px;

        @media only screen and (max-width: 1720px) {
            max-width: 200px;
        }

        @media only screen and (max-width: 1270px) {
            max-width: 190px;
            padding-right: 15px;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            max-width: 180px;
        }

        @media only screen and (max-width: 365px) {
            max-width: 130px;
        }

        img {
            width: 100%;
        }

        &--desktop {
            @media only screen and (max-width: 1160px) {
                display: none;
            }
        }

        &--mobile {
            display: none;
            @media only screen and (max-width: 1160px) {
                display: block;
            }
        }
    }

    &__logo-link {
        display: block;
        padding-top: 10px;
    }

    &__header-points {
        display: flex;
        flex-wrap: wrap;

        @media only screen and (max-width: 1160px) {
            justify-content: center;
            padding-bottom: 30px;
        }
    }

    &__header-point-wrapper {


        @media only screen and (max-width: 840px) {
            flex: 0 0 100%;
            max-width: 100%;
            margin-bottom: 15px;
            text-align: center;
        }

        &:first-child {
            .masthead__header-point-number {
                background: $teal;
            }
            .masthead__header-point {
                &:after {
                    transform: translateX(50%);
                }
            }
        }
        &:last-child {
            .masthead__header-point {
                &:after {
                    transform: translateX(-50%);
                }
            }
        }
    }

    &__header-point {
        position: relative;
        padding: 0 20px;


        &:after {
            content: "";
            display: block;
            position: absolute;
            top: 20px;
            width: 100%;
            height: 1px;
            background: $midlightgrey;

            @media only screen and (max-width: 840px) {
                display: none;
            }
        }
    }

    &__header-point-number {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        margin: 0 auto 5px;
        border-radius: 50%;
        line-height: 1;
        font-family: $headingfontfamily;
        font-size: 2rem;
        font-weight: bold;
        background: $bluegrey;
        color: $white;
    }

    &__header-point-icon {
        position: absolute;
        top: 20px;
        right: 0;
        font-size: 2.5rem;
        transform: translateY(-50%) translateX(50%);
        color: $midlightgrey;

        @media only screen and (max-width: 840px) {
            display: none;
        }
    }

    &__header-point-text {
        color: $bluegrey;
    }

    &__navigation {
        display: inline-block;
        //margin-left: auto;
        position: relative;
        vertical-align: middle;

        @media only screen and (max-width: 1160px) {
            display: none;
        }

        @media only screen and (max-width: 1575px) {
            margin-left: 0;
        }

        nav {
            > ul {
                display: flex;
                flex-wrap: wrap;
                align-items: center;

                > li {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    position: relative;
                    height: 130px;
                    padding: 0 10px;
                    margin: 0;
                    font-size: 1.9rem;

                    @media only screen and (max-width: 1720px) {
                        height: 100px;
                        font-size: 1.6rem;
                    }

                    &:after {
                        content: '';
                        display: block;
                        position: fixed;
                        top: 0;
                        left: 0;
                        z-index: 3;
                        width: 100%;
                        height: 100%;
                        background: rgba($white, 0.55);
                        pointer-events: none;
                        opacity: 0;
                        visibility: hidden;
                        transition: 300ms;
                    }

                    > a {
                        > svg {
                            display: none;
                        }

                        border-top: 3px solid transparent;
                        border-bottom: 3px solid transparent;

                        &:hover,
                        &:focus {
                            border-bottom: 3px solid $teal;
                        }
                    }

                    &.menu-item-has-children {
                        &:hover,
                        &:focus {
                            > a {
                                border-bottom: 3px solid $teal;
                            }

                            &:after {
                                opacity: 1;
                                visibility: visible;
                            }
                        }

                        &:before {
                            content: "";
                            display: block;
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            z-index: 60;
                            width: 0;
                            height: 0;
                            margin: 0 auto;
                            border-top: 0 solid transparent;
                            border-right: 30px solid transparent;
                            border-bottom: 30px solid $white;
                            border-left: 30px solid transparent;
                            opacity: 0;
                            pointer-events: none;
                            visibility: hidden;
                        }
                    }

                    &:hover,
                    &:focus {
                        &:before {
                            opacity: 1;
                            pointer-events: auto;
                            visibility: visible;
                        }

                        .sub-menu-0 {
                            opacity: 1;
                            visibility: visible;
                            pointer-events: auto;
                            transform: translateY(0);
                        }
                    }
                }
            }
        }

        ul {
            padding: 0;
            margin: 0;
            font-size: 0;

            li {
                display: inline-block;
                font-size: 1.8rem;
                font-family: $headingfontfamily;
                font-weight: 300;
                line-height: 1.875;

                @media only screen and (max-width: 1720px) {
                    font-size: 1.6rem;
                }
            }
        }
    }

    &__secondary-navigation {
        position: relative;
        top: -2px;
        display: inline-block;
        padding: 0 40px;

        @media only screen and (max-width: 1810px) {
            padding: 0 15px;
        }

        @media only screen and (max-width: 1575px) {
            padding: 0;
            margin-right: auto;
        }

        @media only screen and (max-width: 1160px) {
            display: none;
        }

        ul {
            padding: 0;
            margin: 0;
        }

        li {
            display: inline-block;
            position: relative;
            margin: 0 10px;
            font-size: 1.4rem;

            @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                margin: 0 6px;
            }

            a {
                display: block;
                position: relative;
                top: 1px;
            }

            &.menu-item-has-children {
                &:hover {
                    .sub-menu {
                        opacity: 1;
                        visibility: visible;
                        pointer-events: auto;
                        transform: translateY(0);
                    }
                }
            }

            > .sub-menu {
                position: absolute;
                top: 100%;
                left: -15px;
                padding: 10px 0;
                background: $white;
                opacity: 0;
                visibility: hidden;
                pointer-events: none;
                transform: translateY(10px);

                li {
                    white-space: nowrap;
                    margin: 0;
                    position:relative;
                    display: block;
                    width: 100%;
                    a {
                        padding: 5px 15px;
                    }
                    > .sub-menu{
                        position:absolute;
                        top: 0;
                        left: 100%;
                        display: none;
                        li{
                            display: block;
                        }
                    }
                    &:hover .sub-menu{
                        display: block;
                    }
                }
            }
        }
    }

    .sub-menu-0 {
        position: absolute;
        top: 100%;
        left: 10px;
        z-index: 4;
        padding: 0;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        background: $white;
        transform: translateY(-15px);
        transition: 100ms;
        box-shadow: 0 0 60px rgba($black, 0.25);

        > ul {
            > li {
                > a {
                    position: relative;
                    z-index: 30;
                    background: $white;
                }

                &:hover,
                &:focus {
                    svg {
                        transform: translateX(15px);
                        color: $teal;
                    }
                }

                &.menu-item-has-children {
                    &:hover,
                    &:focus {
                        .sub-menu-1 {
                            opacity: 1;
                            visibility: visible;
                            pointer-events: auto;
                        }
                    }
                }
            }
        }

        li {
            display: block;
            position: static;
            padding: 0;
            margin: 0;

            &:last-child {
                a {
                    border-bottom: none;
                }
            }

            svg {
                display: block;
                position: absolute;
                top: 0;
                right: 35px;
                bottom: 0;
                margin: auto 0;
                font-size: 2.4rem;
                pointer-events: none;
                transition: 150ms;
            }
        }

        a {
            position: relative;
            white-space: nowrap;
            &:hover,
            &:focus {
                text-shadow: 0px 0px 1px $teal;
            }
        }

        ul {
            .sub-menu-1 {
                position: absolute;
                top: 0;
                left: 100%;
                z-index: 4;
                min-height: 100%;
                height: auto;
                padding: 0 15px;
                background: $white;
                border-left: 1px solid $midlightgrey;
                opacity: 0;
                visibility: hidden;
                pointer-events: none;
                box-shadow: 0 0 60px rgba($black, 0.25);

                li {
                    @media only screen and (max-width: 1720px) {
                        padding: 0;
                    }
                }

                .menu-list-heading {
                    color: $teal!important;
                }

                > ul {
                    display: flex;

                    .menu-column {
                        padding: 15px 0;
                        flex: 1 0 auto;

                        .sub-menu-wrapper {
                            margin-bottom: 20px;
                        }
                    }
                }
            }

            .sub-menu-2 {
                li {
                    font-size: 1.5rem;
                    padding: 0;
                    margin-bottom: 2px;
                }
            }

            .sub-menu {
                li {
                    a {
                        padding: 0;
                        border: none;
                    }

                    svg {
                        display: none;
                    }
                }
            }
        }

        > ul {
            > li {
                &.menu-item-has-children {
                    > a {
                        min-width: 220px;
                        padding: 20px 120px 20px 20px;

                        @media only screen and (max-width: 1720px) {
                            padding: 15px 45px 15px 5px;
                        }
                    }
                }
                > a {
                    display: flex;
                    align-items: center;
                    padding: 13px 120px 13px 20px;
                    //margin: 0 -12px;
                    border-bottom: 1px solid $midlightgrey;
                    overflow: hidden;

                    @media only screen and (max-width: 1720px) {
                        padding: 15px 45px 15px 5px;
                    }
                }
            }
        }

        .menu-thumb {
            flex: 0 0 94px;
            padding: 0 12px;

            @media only screen and (max-width: 1720px) {
                display: none;
            }
        }

        .menu-title {
            display: block;
            flex: 1 0;
            padding: 0 12px;
            &.menu-title-thumb {
                padding-right: 70px;
            }
        }

        .menu-list-heading {
            margin-bottom: 12px;
            font-size: 2.4rem;
            font-family: $headinglightsfontfamily;
            font-weight: 500;
            color: $teal;

            @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
                font-size: 1.6rem;
            }

            &:hover,
            &:focus {
                text-shadow: none;
            }
        }
    }

    &__details {
        margin: 0 -9px 0 auto;
        font-size: 0;
    }

    &__details-item {
        margin: 0 9px;
        vertical-align: middle;

        &:last-child {
            margin-right: 0;
        }

        &--burger {
            display: none;

            @media only screen and (max-width: 1160px) {
                display: inline-block;
            }

            &:hover,
            &:focus {
                .masthead__burger-lines {
                    background: rgba($teal, 1);

                    &:before {
                        background: rgba($teal, 1);
                    }

                    &:after {
                        background: rgba($teal, 1);
                    }
                }
            }
        }
    }

    &__phone {
        display: inline-block;
        font-size: 1.8rem;
        font-family: $headinglightsfontfamily;
        font-weight: 500;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            display: none;
        }


        @media only screen and (max-width: 1160px) {
            display: inline-block;
        }

        svg {
            margin-right: 5px;
        }
    }

    &__trigger {
        display: none;
        position: relative;
        font-size: 1.6rem;
        text-align: center;
        color: $bluegrey;
        cursor: pointer;
        transition: 300ms;

        @media only screen and (max-width: 1160px) {
            color: $white;
        }

        span {
            transition: none;
        }

        &:hover,
        &:focus {
            color: $teal;
        }

        @media only screen and (max-width: 1575px) {
            display: block;
        }
    }

    &__trigger-icon {
        font-size: 2.6rem;
    }

    &__trigger-text {
        font-size: 0.8rem;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        font-weight: 400;
        font-family: $bodyfontfamily;
    }

    &__trigger-desktop {
        @media only screen and (max-width: 1575px) {
            display: none;
        }
    }

    &__form-wrapper {
        display: inline-block;
    }

    &__form-container {
        @media only screen and (max-width: 1575px) {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            padding-right: 60px;
            opacity: 0;
            transform: translateY(-10px);
            transition: 300ms;
            pointer-events: none;

            &.active {
                opacity: 1;
                pointer-events: auto;
                transform: translateY(0);
            }
        }
    }

    &__form {
        position: relative;

        input {
            width: 240px;
            font-size: 1.6rem;

            @media only screen and (max-width: 1810px) {
                width: 200px;
            }

            @media only screen and (max-width: 1575px) {
                width: 100%;
                border: none;
            }
        }

        ::placeholder {
            font-family: $headingfontfamily;
            font-style: italic;
            color: #b5b5b5;
        }

        :-ms-input-placeholder {
            color: #b5b5b5;
        }

        ::-ms-input-placeholder {
            color: #b5b5b5;
        }

        button {
            position: absolute;
            top: 0;
            right: 0;
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 0;
            font-size: 2.5rem;
            background: none;
            color: $bluegrey;
            transition: 300ms;
            outline: 0;

            &:hover,
            &:focus {
                color: $teal;
            }
        }
    }

    &__form-close {
        display: block;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 60px;
        height: 100%;
        background: $white;
        color: $bluegrey;
        cursor: pointer;
        transition: 300ms;

        &:hover,
        &:focus {
            color: $teal;
        }

        @media only screen and (min-width: 1551px) {
            display: none;
        }

        svg {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            margin: auto;
            font-size: 2.6rem;
        }
    }

    &__contact-link {
        display: inline-block;

        @media only screen and (max-width: 1160px) {
            display: none;
        }


        .button {
            padding: 14px 30px;
            border-radius: 0;
            border: 1px solid $bluegrey;
            background: transparent;

            @media only screen and (max-width: 1575px) {
                padding: 12px 20px;
                font-size: 1.6rem;
                min-width: 0;
            }

            &:hover,
            &:focus {
                background: $bluegrey;
                color: $white!important;
            }
        }
    }
}

.review-bar {
    position: absolute;
    left: 0;
    bottom: -44px;
    z-index: 1; // Much lower than masthead__inner (51) to stay behind it
    width: 100%;
    height: 45px;
    padding: 5px 0;
    text-align: center;
    background: rgba($white, 0.6);

    &.active {
        z-index: 2; // Still much lower than masthead__inner
    }

    &--grey {
        background: $offwhitethree;
    }

    // Scroll hide/show behavior
    &.is-visible {
        transition: 300ms;
        transform: translateY(0);
        z-index: 1; // Keep it low but positive
    }

    &.is-hidden {
        transition: 300ms;
        transform: translateY(-100%);
        z-index: -1; // Negative z-index to force it behind everything
    }

    // Mobile responsive styling
    @media only screen and (max-width: 480px) {
        height: 35px;
        bottom: -35px;
        padding: 3px 0;

        .aito-widget,
        .aito-php-widget {
            font-size: 14px !important;
            gap: 6px !important;
        }

        .aito-tick {
            width: 16px !important;
            height: 16px !important;
        }
    }

    // Very small screens
    @media only screen and (max-width: 360px) {
        height: 30px;
        bottom: -30px;
        padding: 2px 0;

        .aito-widget,
        .aito-php-widget {
            font-size: 12px !important;
            gap: 4px !important;
        }

        .aito-tick {
            width: 14px !important;
            height: 14px !important;
        }
    }

}

.navigation {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 989;
    width: 100%;
    max-width: 320px;
    height: 100%;
    background: $white;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    transform: translateX(101%);
    transition: 600ms;

    &.active {
        transform: translateX(0);
    }

    &.submenu-active {
        overflow: hidden;
    }

    &__inner {
        padding-top: 80px;
    }

    &__close {
        position: absolute;
        top: 20px;
        right: 10px;
        z-index: 120;
        width: 40px;
        height: 40px;
        font-size: 2.6rem;
        text-align: center;
        cursor: pointer;
        transition: 300ms;
        background: $white;

        &:hover,
        &:focus {
            color: $bluegrey;
        }
    }

    .menu-item-has-children {
        position: relative;
        padding-right: 45px;
    }

    ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    li {
        margin: 0;
        font-size: 1.5rem;
        line-height: 1.875;
    }

    .sub-menu {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 101;
        width: 100%;
        max-width: 320px;
        height: 100%;
        pointer-events: none;
        padding-top: 80px;
        padding-bottom: 40px;
        background: $white;
        transform: translateX(101%);
        transition: 600ms;
        overflow: hidden;

        &.active {
            transform: translateX(0);
            overflow-x: hidden;
            overflow-y: auto;
            pointer-events: auto;
        }
    }

    .sub-arrow {
        display: block;
        position: absolute;
        right: 0;
        top: 0;
        width: 45px;
        height: 100%;
        background: $bluegrey;
        transition: 300ms;
        cursor: pointer;

        &:hover,
        &:focus {
            background: $teal;
        }

        &:after {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            right: 4px;
            bottom: 0;
            left: 0;
            width: 10px;
            height: 10px;
            margin: auto;
            border-top: 2px solid $white;
            border-right: 2px solid $white;
            transform: rotate(45deg);
        }
    }

    a {
        display: block;
        text-decoration: none;
        padding: 12px 15px;
        border-top: 1px solid $lightergrey;
        font-weight: 600;

        &:hover,
        &:focus {
            background: $teal;
            border-color: $teal;
            color: $white;
        }
    }
}

.aito-link {
    display: inline-block;
}


.masthead__navigation {
    li.mobile{
        display: none;
    }
    #menu-item-818 .sub-menu-1 li{
    &.menu-item-object-holiday{
        display: none;
        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3){
            display: block;
        }
    }
    &.menu-item-object-holiday-type{

        &:nth-child(4):last-child{
            display: none;
        }
    }}
}
.pagename-make-a-payment .masthead--form-page{
    display: none;
}

.has-overlay .masthead{
    bottom:0;
    .review-bar{
        z-index: 99999;
    }
}
